package com.wanlianyida.bss.interfaces.facade;


import com.wanlianyida.bss.application.model.command.platform.ExterPlatformParameterCommand;
import com.wanlianyida.bss.application.model.dto.platform.PlatformParameterDTO;
import com.wanlianyida.bss.application.model.query.platform.ExterPlatformParameterQuery;
import com.wanlianyida.bss.application.service.PlatformParameterAppService;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@RequestMapping("/platform-parameter")
@Slf4j
@RestController
public class PlatformParameterController {


    @Resource
    private PlatformParameterAppService platformParameterAppService;

    /**
     * 修改参数值
     */
    @PostMapping(value = "/update-para-value-by-code")
    public ResultMode<?> updateParaValueByParaCode(@RequestBody @Validated ExterPlatformParameterCommand command) {
        return platformParameterAppService.updateParaValueByParaCode(command);
    }

    /**
     * 根据参数编码查询参数值
     */
    @PostMapping(value = "/para-value-by-code")
    public ResultMode<PlatformParameterDTO> getParaValueByParaCode(@RequestBody @Validated ExterPlatformParameterQuery query) {
        return platformParameterAppService.getParaValueByParaCode(query);
    }


}
