package com.wanlianyida.platform.interfaces.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.wanlianyida.framework.lgicache.impl.RedisService;
import com.wanlianyida.framework.lgicache.lock.DistributedLocker;
import com.wanlianyida.platform.application.service.supervise.WaybillAppService;
import com.wanlianyida.platform.infrastructure.enums.RedisKeyEnum;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 监管定时任务
 */
@Slf4j
@Component
public class SuperviseJob {

    @Resource
    private RedisService redisService;

    @Resource
    private DistributedLocker distributedLocker;

    @Resource
    private WaybillAppService waybillAppService;

    /**
     * 监管运单合同同步失败-重新拉取
     */
    @XxlJob("spvContractSyncErrorJob")
    public void spvContractSyncErrorJob() {
        log.info("spvContractSyncErrorJob#监管运单合同同步失败-重新拉取开始");
        String key = RedisKeyEnum.SPV_CONTRACT_SYNC_ERROR_JOB.getCode();
        boolean tryLock = distributedLocker.tryLock(key, 0, 0, TimeUnit.SECONDS);
        if (!tryLock) {
            log.info("spvContractSyncErrorJob#获取锁失败,{}", key);
            return;
        }

        //参数传入的运单号集合
        List<String> waybillIds = null;
        String param = XxlJobHelper.getJobParam();
        log.info("spvContractSyncErrorJob#params: {}", JSONUtil.toJsonStr(param));
        if (StrUtil.isNotBlank(param)) {
            waybillIds = JSONUtil.toList(param, String.class);
        }

        try {
            if (IterUtil.isEmpty(waybillIds)) {
                //redis获取运单号
                Map<String, Object> waybillMap = redisService.hGetAll(RedisKeyEnum.SPV_CONTRACT_SYNC_ERROR.getCode());
                if (MapUtil.isEmpty(waybillMap)) {
                    log.info("spvContractSyncErrorJob#没有需要重新拉取的运单");
                    return;
                }
                waybillIds = CollUtil.newArrayList(waybillMap.keySet());
            }

            for (String waybillId : waybillIds) {
                log.info("spvContractSyncErrorJob#重新拉取运单ID:{}", waybillId);
                //重新拉取合同
                waybillAppService.spvContractSyncError(waybillId);
                //拉取成功，删除Redis
                redisService.hDel(RedisKeyEnum.SPV_CONTRACT_SYNC_ERROR.getCode(), waybillId);
            }
        } catch (Exception e) {
            log.error("spvContractSyncErrorJob#异常", e);
        } finally {
            distributedLocker.unlock(key);
        }

        log.info("spvContractSyncErrorJob#监管运单合同同步失败-重新拉取完成");
    }

}
