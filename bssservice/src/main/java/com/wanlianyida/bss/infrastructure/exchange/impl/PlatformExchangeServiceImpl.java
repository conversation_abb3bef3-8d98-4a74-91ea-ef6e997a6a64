package com.wanlianyida.bss.infrastructure.exchange.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.IterUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.isoftstone.hig.annotations.LogPrintPoint;
import com.isoftstone.hig.common.model.ResultMode;
import com.isoftstone.hig.common.utils.JwtUtil;
import com.isoftstone.hig.common.utils.exception.WlydException;
import com.isoftstone.hig.platform.api.entity.*;
import com.isoftstone.hig.platform.api.filter.PlatformCmOperationMainBodyFilter;
import com.isoftstone.hig.platform.api.filter.PlatformUmCompanyFilter;
import com.isoftstone.hig.platform.api.inter.*;
import com.isoftstone.hig.platform.api.mvcvo.LoginVo;
import com.wanlianyida.bss.api.dto.BssMenuFunctionDTO;
import com.wanlianyida.bss.api.dto.BssUserBaseInfoDTO;
import com.wanlianyida.bss.api.dto.BssUserLoginDTO;
import com.wanlianyida.bss.api.dto.NetworkMainBodyListDTO;
import com.wanlianyida.bss.api.query.BssUserLoginQuery;
import com.wanlianyida.bss.application.model.command.platform.ExterPlatformParameterCommand;
import com.wanlianyida.bss.application.model.dto.platform.PlatformParameterDTO;
import com.wanlianyida.bss.application.model.query.platform.ExterPlatformParameterQuery;
import com.wanlianyida.bss.infrastructure.exchange.PlatformExchangeService;
import com.wanlianyida.platform.api.inter.OperationMainBodyInter;
import com.wanlianyida.platform.api.inter.PlatformParameterInter;
import com.wanlianyida.platform.api.model.command.ParaValueByParaCodeCommand;
import com.wanlianyida.platform.api.model.dto.AllowModifyWaybillEndAddrFlagDTO;
import com.wanlianyida.platform.api.model.dto.PlatformCmPlatformParameterDTO;
import com.wanlianyida.platform.api.model.query.AllowModifyWaybillEndAddrFlagQuery;
import com.wanlianyida.platform.api.model.query.PlatformParameterQuery;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import javax.annotation.Resource;

/**
 * platform 调用层
 */
@Slf4j
@Component
public class PlatformExchangeServiceImpl implements PlatformExchangeService {

    @Autowired
    private AuthorityNoLoginInter authorityNoLoginInter;

    @Autowired
    private PlatformSubSystemFunctionInter platformSubSystemFunctionInter;

    @Autowired
    private PlatformUmUserbaseinfoInter platformUmUserbaseinfoInter;
    @Resource
    private PlatformUmCompanyInter platformUmCompanyInter;

    @Resource
    private PlatformCmPlatformParameterInter platformCmPlatformParameterInter;

    @Resource
    private PlatformCmOperationMainBodyInter platformCmOperationMainBodyInter;

    @Resource
    private OperationMainBodyInter operationMainBodyInter;

    @Resource
    private PlatformParameterInter platformParameterInter;


    /**
     * 登录
     * @param bssUserLoginQuery
     * @return
     */
    @LogPrintPoint
    @Override
    public BssUserLoginDTO login(BssUserLoginQuery bssUserLoginQuery) {
        if(ObjUtil.isNull(bssUserLoginQuery)){
            return null;
        }
        PlatformUmLogininfo platformUmLogininfo = BeanUtil.copyProperties(bssUserLoginQuery, PlatformUmLogininfo.class);
        ResultMode<LoginVo> loginVoResultMode = authorityNoLoginInter.login(platformUmLogininfo);
        log.info("login#登录请求参数：{},返回结果：{}", JSONUtil.toJsonStr(bssUserLoginQuery),JSONUtil.toJsonStr(loginVoResultMode));
        if(ObjUtil.isNull(loginVoResultMode) || ObjUtil.isNull(IterUtil.getFirst(loginVoResultMode.getModel()))){
            throw new WlydException(loginVoResultMode.getErrMsg());
        }
        if(!loginVoResultMode.isSucceed()){
            throw new WlydException(loginVoResultMode.getErrMsg());
        }
        return BeanUtil.copyProperties(IterUtil.getFirst(loginVoResultMode.getModel()), BssUserLoginDTO.class);
    }

    /**
     * 获取当前登录用户权限
     * @return
     */
    @Override
    public List<BssMenuFunctionDTO> getUserMenuFunction() {
        ResultMode<PlatformUmFunction> userMenuFunction = platformSubSystemFunctionInter.getUserFunction("3");
        if(ObjUtil.isNull(userMenuFunction)){
            return new ArrayList<>();
        }
        if(!userMenuFunction.isSucceed()){
            throw new WlydException(userMenuFunction.getErrMsg());
        }
        return BeanUtil.copyToList(userMenuFunction.getModel(),BssMenuFunctionDTO.class);
    }

    /**
     *登出
     */
    @Override
    public String webLogout() {
        ResultMode<String> resultMode = platformUmUserbaseinfoInter.webLogout();
        if(ObjUtil.isNull(resultMode) || ObjUtil.isNull(resultMode.getModel())){
            return null;
        }
        if(!resultMode.isSucceed()){
            throw new WlydException(resultMode.getErrMsg());
        }
        return IterUtil.getFirst(resultMode.getModel());
    }

    /**
     * 获取当前登录用户信息
     * @return
     */
    @Override
    public BssUserBaseInfoDTO getUserInfo() {
        ResultMode<PlatformUmUserbaseinfo> resultMode = platformUmUserbaseinfoInter.getUserInfo();
        if(ObjUtil.isNull(resultMode)){
            return null;
        }
        if(!resultMode.isSucceed()){
            throw new WlydException(resultMode.getErrMsg());
        }
        return BeanUtil.copyToList(resultMode.getModel(),BssUserBaseInfoDTO.class).get(0);
    }

    /**
     * 批量查询企业信息
     */
    @Override
    public List<PlatformUmCompany> batchQueryCompany(List<String> companyIdList) {
        if (CollectionUtil.isEmpty(companyIdList)) {
            return Collections.EMPTY_LIST;
        }
        PlatformUmCompanyFilter filter = new PlatformUmCompanyFilter();
        filter.setExCompanyIdList(companyIdList);
        return platformUmCompanyInter.getCompanyByModel(filter).getModel();
    }

    @Override
    public PlatformCmPlatformParameter getByParaCodes(List<String> paraCodes) {
        if(CollUtil.isEmpty(paraCodes)){
            return null;
        }
        ResultMode<PlatformCmPlatformParameter> resultMode= platformCmPlatformParameterInter.getByParaCodes(paraCodes);
        if (!ObjUtil.isNull(resultMode) && CollUtil.isNotEmpty(resultMode.getModel())) {
            return resultMode.getModel().get(0);
        }
        return null;
    }

    /**
     * 查询货运货运主体列表
     * @param type
     * @return
     */
    @Override
    public List<NetworkMainBodyListDTO> getMainBodyList(String type) {
        log.info("getMainBodyList#请求参数：{}",type);
        if(StrUtil.isBlank(type)){
            return null;
        }

        PlatformCmOperationMainBodyFilter filter = new PlatformCmOperationMainBodyFilter();
        filter.setType(type);
        ResultMode<PlatformCmOperationMainBody> resultMode = platformCmOperationMainBodyInter.getMainBodyList(filter);
        if(ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || CollUtil.isEmpty(resultMode.getModel())){
            return null;
        }
        return BeanUtil.copyToList(resultMode.getModel(),NetworkMainBodyListDTO.class);
    }


    @Override
    public String queryAllowModifyWaybillEndAddrFlag(String networkMainBodyId) {
        AllowModifyWaybillEndAddrFlagQuery query = new AllowModifyWaybillEndAddrFlagQuery();
        query.setOperationMainBodyId(networkMainBodyId);
        com.wanlianyida.framework.lgicommon.entity.ResultMode<AllowModifyWaybillEndAddrFlagDTO> resultMode = operationMainBodyInter.queryAllowModifyWaybillEndAddrFlag(query);
        if (!resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())) {
            return "";
        }
        return resultMode.getModel().get(0).getAllowModifyWaybillEndAddrFlag();
    }

    @Override
    public com.wanlianyida.framework.lgicommon.entity.ResultMode<?> updateParaValueByParaCode(ExterPlatformParameterCommand command) {
        command.setModifyBy(JwtUtil.getInstance().getTokenInfo().getUserBaseId());
        return platformParameterInter.updateParaValueByParaCode(BeanUtil.toBean(command, ParaValueByParaCodeCommand.class));
    }

    @Override
    public PlatformParameterDTO getParaValueByParaCode(ExterPlatformParameterQuery query) {
        com.wanlianyida.framework.lgicommon.entity.ResultMode<PlatformCmPlatformParameterDTO> resultMode = platformParameterInter.getParaValueByParaCode(BeanUtil.toBean(query, PlatformParameterQuery.class));
        if(ObjUtil.isNull(resultMode) || !resultMode.isSucceed() || IterUtil.isEmpty(resultMode.getModel())){
            return null;
        }
        return BeanUtil.toBean(IterUtil.getFirst(resultMode.getModel()),PlatformParameterDTO.class);
    }
}
