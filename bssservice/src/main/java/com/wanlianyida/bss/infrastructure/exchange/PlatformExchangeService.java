package com.wanlianyida.bss.infrastructure.exchange;

import com.isoftstone.hig.platform.api.entity.PlatformCmPlatformParameter;
import com.isoftstone.hig.platform.api.entity.PlatformUmCompany;
import com.wanlianyida.bss.api.dto.BssMenuFunctionDTO;
import com.wanlianyida.bss.api.dto.BssUserBaseInfoDTO;
import com.wanlianyida.bss.api.dto.BssUserLoginDTO;
import com.wanlianyida.bss.api.dto.NetworkMainBodyListDTO;
import com.wanlianyida.bss.api.query.BssUserLoginQuery;
import com.wanlianyida.bss.application.model.command.platform.ExterPlatformParameterCommand;
import com.wanlianyida.bss.application.model.dto.platform.PlatformParameterDTO;
import com.wanlianyida.bss.application.model.query.platform.ExterPlatformParameterQuery;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;

import java.util.List;

/**
 * platform 调用层
 */
public interface PlatformExchangeService {

    /**
     * 登录
     * @param bssUserLoginQuery
     * @return
     */
    BssUserLoginDTO login(BssUserLoginQuery bssUserLoginQuery);



    /**
     * 获取当前登录用户权限
     * @return
     */
    List<BssMenuFunctionDTO> getUserMenuFunction();

    /**
     *登出
     */
    String webLogout();

    /**
     * 获取当前登录用户信息
     * @return
     */
    BssUserBaseInfoDTO getUserInfo();

    List<PlatformUmCompany> batchQueryCompany(List<String> companyIdList);

    /**
     * 查询字典值
     * @param paraCodes
     * @return
     */
    PlatformCmPlatformParameter getByParaCodes(List<String> paraCodes);

    /**
     * 查询网络货运主体列表
     * @param type
     * @return
     */
    List<NetworkMainBodyListDTO> getMainBodyList(String type);

    String queryAllowModifyWaybillEndAddrFlag(String networkMainBodyId);

    ResultMode<?> updateParaValueByParaCode(ExterPlatformParameterCommand command);

    PlatformParameterDTO getParaValueByParaCode(ExterPlatformParameterQuery query);
}
