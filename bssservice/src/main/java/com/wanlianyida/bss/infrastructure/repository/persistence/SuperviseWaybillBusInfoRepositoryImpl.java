package com.wanlianyida.bss.infrastructure.repository.persistence;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wanlianyida.bss.domain.entity.supervise.SuperviseWaybillBusInfo;
import com.wanlianyida.bss.domain.repository.SuperviseWaybillBusInfoRepository;
import com.wanlianyida.bss.infrastructure.repository.mapper.supervise.SuperviseWaybillBusInfoMapper;
import com.wanlianyida.bss.infrastructure.repository.po.supervise.SuperviseWaybillBusInfoPO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年07月11日 14:51
 */
@Component
public class SuperviseWaybillBusInfoRepositoryImpl implements SuperviseWaybillBusInfoRepository {

    @Resource
    private SuperviseWaybillBusInfoMapper superviseWaybillBusInfoMapper;

    @Override
    public SuperviseWaybillBusInfo queryByWaybillId(String waybillId) {
        QueryWrapper<SuperviseWaybillBusInfoPO> wrapper = new QueryWrapper<>();
        wrapper.eq("shipping_note_number", waybillId);
        return BeanUtil.toBean(superviseWaybillBusInfoMapper.selectOne(wrapper), SuperviseWaybillBusInfo.class);
    }
}
