package com.wanlianyida.bss.infrastructure.util.lock;

import com.isoftstone.hig.common.utils.RedisUtil;
import com.isoftstone.hig.common.utils.inter.DistributedLocker;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RLock;
import org.redisson.api.RMap;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Slf4j
@Component
public class RedissonDistributedLocker implements DistributedLocker {


    @Override
    public RLock lock(String lockKey) {
          return  RedisUtil.getRedisson().getLock(lockKey);
    }

    @Override
    public void expire(String key, int expire, TimeUnit unit) {
        RMap rMap =  RedisUtil.getRedisson().getMap(key);
        rMap.expire(expire, unit);
    }

    @Override
    public RLock getLock(String key) {
        return RedisUtil.getRedisson().getLock(key);
    }

    @Override
    public Long addAndGet(String key, int num) {
        RAtomicLong atomicLong =  RedisUtil.getRedisson().getAtomicLong(key);
        Long old = atomicLong.incrementAndGet();
        atomicLong.set(old + num);
        atomicLong.incrementAndGet();
        return atomicLong.get();
    }

    @Override
    public Long incrementAndGet(String key) {

        return  RedisUtil.getRedisson().getAtomicLong(key).incrementAndGet();
    }

    /**
     * 删除key
     * @param key
     */
    @Override
    public void del(String key){
        RMap rMap =  RedisUtil.getRedisson().getMap(key);
        rMap.delete();
    }

    @Override
    public RLock lock(String lockKey, int timeout) {
        RLock rLock =  RedisUtil.getRedisson().getLock(lockKey);
        rLock.lock(timeout, TimeUnit.SECONDS);
        return rLock;
    }

    @Override
    public RLock lock(String lockKey, TimeUnit unit, int timeout) {
        RLock rLock =  RedisUtil.getRedisson().getLock(lockKey);
        rLock.lock(timeout, unit);
        return rLock;
    }

    @Override
    public boolean tryLock(String lockKey, TimeUnit unit, int waitTime) {
        RLock lock =  RedisUtil.getRedisson().getLock(lockKey);
        try {
            return lock.tryLock(waitTime, unit);
        } catch (Exception e) {
            log.error("尝试获取锁失败：{}", e);
            return false;
        }
    }

    @Override
    public boolean tryLock(String lockKey, TimeUnit unit, int waitTime, int leaseTime) {
        RLock lock =  RedisUtil.getRedisson().getLock(lockKey);
        try {
            return lock.tryLock(waitTime, leaseTime, unit);
        } catch (Exception e) {
            log.error("尝试获取锁失败：{}", e);
            return false;
        }
    }

    @Override
    public void unlock(String lockKey) {
        try {
            RLock lock =  RedisUtil.getRedisson().getLock(lockKey);
            if (lock != null && lock.isHeldByCurrentThread()) {
                lock.unlock();
            }
        } catch (Exception e) {
            String msg = String.format("UNLOCK FAILED: key=%s", lockKey);
            log.error("unlock#errmsg:{},errinfo:{}",msg,e);
        }
    }

    @Override
    public void unlock(RLock lock) {
        lock.unlock();
    }


}
