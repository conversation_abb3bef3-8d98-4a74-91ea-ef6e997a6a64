package com.wanlianyida.bss.infrastructure.repository.mapper.cmd;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.wanlianyida.bss.domain.bo.ModifyWaybillDateCarBO;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年07月11日 13:12
 */
@DS("cmd")
public interface CmdCarMapper {

    /**
     * 修改运单时间查询车辆信息
     */
    ModifyWaybillDateCarBO queryByModifyDate(@Param("carPlateNo") String carPlateNo, @Param("carColor") Integer carColor);
}
