package com.wanlianyida.bss.infrastructure.repository.po.tms;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年07月11日 15:38
 */
@Data
@TableName("tms_waybill_change_record")
public class TmsWaybillChangeRecordPO extends Model<TmsWaybillChangeRecordPO> {

    @TableId
    private Long id;

    @ApiModelProperty("运单号")
    private String waybillId;

    @ApiModelProperty("订单号")
    private String orderId;

    @ApiModelProperty("货源号")
    private String goodsId;

    @ApiModelProperty("货源名称")
    private String goodsName;

    @ApiModelProperty("变更来源[10-3pl,20-bss]")
    private Integer changeSource;

    @ApiModelProperty("申请原因")
    private String applyReason;

    @ApiModelProperty("申请公司id")
    private String applyCompanyId;

    @ApiModelProperty("审核类型[10-修改单价,20-修改目的地,30-修改出发地,40-修改接单时间]")
    private Integer auditType;

    @ApiModelProperty("审核方式[10-自动审核,20-人工审核]")
    private Integer auditMethod;

    @ApiModelProperty("审核状态[10-待审核,20-审核通过,30-审核驳回]")
    private Integer auditStatus;

    @ApiModelProperty("审核备注")
    private String auditRemark;

    @ApiModelProperty("修改状态[10-待处理,20-处理中,30-处理完成]")
    private Integer modifyStatus;

    @ApiModelProperty("变更的值(json)")
    private String changeValue;

    @ApiModelProperty("货运类型[1-传统模式(默认),2-网络模式]")
    private String freightType;

    @ApiModelProperty("托运企业id")
    private String shipperCompanyId;

    @ApiModelProperty("托运企业名称")
    private String shipperCompanyName;

    @ApiModelProperty("托运企业简称")
    private String shipperCompanyShortName;

    @ApiModelProperty("网络货运主体id")
    private String networkMainBodyId;

    @ApiModelProperty("网络货运主体名称")
    private String networkMainBodyName;

    private String creatorId;

    private String creatorName;

    private Date createTime;

    private String auditorId;

    private String auditorName;

    private Date auditTime;

    private String lastUpdaterId;

    private String lastUpdaterName;

    private Date lastUpdateTime;
}
