package com.wanlianyida.bss.infrastructure.repository.persistence;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.wanlianyida.bss.domain.entity.lsds.GoodsSourceEntity;
import com.wanlianyida.bss.domain.repository.GoodsSourceRepository;
import com.wanlianyida.bss.infrastructure.repository.mapper.lsds.GoodsSourceMapper;
import com.wanlianyida.bss.infrastructure.repository.po.lsds.GoodsSourcePO;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年07月15日 14:06
 */
@Component
public class GoodsSourceRepositoryImpl implements GoodsSourceRepository {

    @Resource
    private GoodsSourceMapper goodsSourceMapper;

    @Override
    public GoodsSourceEntity queryByGoodsId(String goodsId) {
        QueryWrapper<GoodsSourcePO> wrapper = new QueryWrapper<>();
        wrapper.eq("goods_id", goodsId);
        GoodsSourcePO goodsSource = goodsSourceMapper.selectOne(wrapper);
        return BeanUtil.toBean(goodsSource, GoodsSourceEntity.class);
    }
}
