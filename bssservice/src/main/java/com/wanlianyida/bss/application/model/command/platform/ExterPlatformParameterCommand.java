package com.wanlianyida.bss.application.model.command.platform;

import com.wanlianyida.framework.lgicommon.model.base.ExternalBaseRequest;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 参数配置
 */
@Data
@ApiModel(description = "参数配置")
public class ExterPlatformParameterCommand extends ExternalBaseRequest {

    @ApiModelProperty(value = "参数编码", name = "paraCode")
    @NotBlank(message = "参数编码不能为空")
    private String paraCode;

    @ApiModelProperty(value = "参数值", name = "paraValue")
    @NotBlank(message = "参数值不能为空")
    private String paraValue;

    @ApiModelProperty(value = "修改人", name = "modifyBy")
    private String modifyBy;

}
