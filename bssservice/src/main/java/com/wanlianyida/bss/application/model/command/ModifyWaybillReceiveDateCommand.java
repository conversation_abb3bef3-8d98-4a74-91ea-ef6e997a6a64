package com.wanlianyida.bss.application.model.command;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @Description 修改运单接单时间
 * @Date 2025年07月11日 09:15
 */
@Data
public class ModifyWaybillReceiveDateCommand {

    @NotBlank(message = "运单号不能为空")
    private String waybillId;

    @NotNull(message = "接单时间不能为空")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date receiveDate;

    @Length(max = 100, message = "申请原因长度不能超过100个字符")
    private String applyReason;
}
