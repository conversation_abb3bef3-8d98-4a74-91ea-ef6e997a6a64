package com.wanlianyida.bss.application.model.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年07月14日 14:04
 */
@Data
public class ModifyDateWaybillDTO {

    @ApiModelProperty("运单号")
    private String waybillId;

    @ApiModelProperty("订单号")
    private String orderId;

    @ApiModelProperty("货源号")
    private String goodsId;

    @ApiModelProperty("货运类型:1-传统模式(默认)、 2-网络模式")
    private String freightType;

    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createDate;

    @ApiModelProperty("司机创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date driverCreateDate;

    @ApiModelProperty("车辆创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date carCreateDate;
}
