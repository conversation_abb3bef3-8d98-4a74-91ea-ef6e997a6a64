package com.wanlianyida.bss.application.service;


import com.wanlianyida.bss.application.model.command.platform.ExterPlatformParameterCommand;
import com.wanlianyida.bss.application.model.dto.platform.PlatformParameterDTO;
import com.wanlianyida.bss.application.model.query.platform.ExterPlatformParameterQuery;
import com.wanlianyida.bss.infrastructure.exchange.PlatformExchangeService;
import com.wanlianyida.framework.lgicommon.entity.ResultMode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class PlatformParameterAppService {


    @Resource
    private PlatformExchangeService platformExchangeService;

    /**
     * 根据参数编码查询参数值
     * @param query
     * @return
     */
    public ResultMode<PlatformParameterDTO> getParaValueByParaCode(ExterPlatformParameterQuery query) {
        ResultMode<PlatformParameterDTO> resultMode = new ResultMode<>();
        resultMode.getModel().add(platformExchangeService.getParaValueByParaCode(query));
        return resultMode;
    }

    /**
     * 修改参数值
     * @param command
     * @return
     */
    public ResultMode<?> updateParaValueByParaCode(ExterPlatformParameterCommand command) {
        return platformExchangeService.updateParaValueByParaCode(command);
    }
}
