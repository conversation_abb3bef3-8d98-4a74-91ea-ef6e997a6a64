package com.wanlianyida.bss.application.model.query;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2025年07月14日 14:23
 */
@Data
public class TmsWaybillChangeRecordQuery {

//    @NotBlank(message = "运单号不能为空")
    @ApiModelProperty("运单号")
    private String waybillId;

    @NotNull(message = "审核类型不能为空")
    @ApiModelProperty("审核类型[10-修改单价,20-修改目的地,30-修改出发地,40-修改接单时间]")
    private Integer auditType;
}
