<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanlianyida.bss.infrastructure.repository.mapper.cmd.CmdDriverIdentityCardMapper">

    <select id="queryByModifyDate" resultType="com.wanlianyida.bss.domain.bo.ModifyWaybillDateDriverBO">
        select
        driver_identity_card_id driverIdentityCardId,
        driver_id driverId,
        license_no licenseNo,
        name,
        create_date createDate
        from cmd_driver_identity_card
        where
        license_no = #{driverCardId}
    </select>
</mapper>