<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wanlianyida.bss.infrastructure.repository.mapper.cmd.CmdCarMapper">

    <select id="queryByModifyDate" resultType="com.wanlianyida.bss.domain.bo.ModifyWaybillDateCarBO">
        select
        car_cmd_id carCmdId,
        car_plate_no carPlateNo,
        car_color carColor,
        create_date createDate
        from cmd_car
        where
        car_plate_no = #{carPlateNo}
        and car_color = #{carColor}
    </select>
</mapper>